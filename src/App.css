/* Professional Typography Import */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

/* Professional Design System Variables */
:root {
  /* Primary Colors */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;

  /* Neutral Colors */
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;

  /* Semantic Colors */
  --success-50: #f0fdf4;
  --success-500: #22c55e;
  --success-600: #16a34a;
  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Icon Sizes */
  --icon-xs: 0.75rem;
  --icon-sm: 1rem;
  --icon-md: 1.25rem;
  --icon-lg: 1.5rem;
  --icon-xl: 2rem;

  /* Typography System */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', 'Roboto', sans-serif;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Font Sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;

  /* Line Heights */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;

  /* Letter Spacing */
  --tracking-tight: -0.025em;
  --tracking-normal: 0em;
  --tracking-wide: 0.025em;
}

/* Base font size scaling for different screen sizes */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  font-size: clamp(14px, 1.2vw, 18px);
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #f8f9fa;
}

#root {
  width: 100%;
  height: 100%;
}

.app {
  position: relative;
  height: 100vh;
  width: 100vw;
  font-family: var(--font-family-primary);
  overflow: hidden;
  font-size: var(--text-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-tight);
  box-sizing: border-box;
  background-color: #f8f9fa;
}

.toolbar {
  position: fixed;
  top: var(--space-4);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  background-color: white;
  backdrop-filter: blur(10px);
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  flex-wrap: nowrap;
  max-width: min(95vw, 1400px);
  overflow: visible;
  animation: slideDown 0.3s ease-out;
  height: 48px; /* Compact height */
}

/* Room Code Search Status Styles */
.room-code-search-status {
  position: fixed;
  top: 120px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  padding: 16px;
  margin: 0;
  font-size: 14px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 999;
  max-width: min(90vw, 600px);
  min-width: 400px;
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.search-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon {
  font-size: 16px;
}

.status-text {
  font-weight: 500;
  color: #495057;
}

.search-actions {
  display: flex;
  gap: 8px;
}

.start-search-btn, .stop-search-btn, .reset-search-btn {
  padding: 6px 12px;
  border: 1px solid #ccc;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
  font-family: inherit;
}

.start-search-btn:hover {
  background: #e3f2fd;
  border-color: #2196f3;
}

.stop-search-btn:hover {
  background: #ffebee;
  border-color: #f44336;
}

.reset-search-btn:hover {
  background: #f3e5f5;
  border-color: #9c27b0;
}

.search-progress {
  margin-bottom: 12px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 4px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4caf50, #2196f3);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #6c757d;
  text-align: center;
}

.search-results {
  border-top: 1px solid #dee2e6;
  padding-top: 12px;
}

.results-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  border-radius: 6px;
  background: white;
  border: 1px solid #e9ecef;
}

.stat-item.found {
  background: #e8f5e8;
  border-color: #c3e6c3;
}

.stat-item.not-found {
  background: #fff3cd;
  border-color: #ffeaa7;
}

.stat-item.percentage {
  background: #e3f2fd;
  border-color: #bbdefb;
}

.stat-label {
  font-size: 11px;
  color: #6c757d;
  margin-bottom: 2px;
  text-align: center;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #495057;
}

.search-error {
  color: #dc3545;
  background: #f8d7da;
  padding: 8px;
  border-radius: 6px;
  border: 1px solid #f5c6cb;
  font-size: clamp(11px, 1.2vw, 14px);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.toolbar button {
  padding: clamp(6px, 0.8vh, 12px) clamp(8px, 1.2vw, 16px);
  border: 1px solid rgba(0, 0, 0, 0.2);
  background-color: rgba(255, 255, 255, 0.95);
  color: #333 !important;
  border-radius: clamp(12px, 1.5vw, 24px);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: clamp(11px, 1.1vw, 14px);
  font-weight: 500;
  white-space: nowrap;
  min-width: auto;
}

.toolbar button:hover {
  background-color: rgba(240, 240, 240, 0.95);
  color: #222 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.toolbar button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  color: #666 !important;
  background-color: rgba(240, 240, 240, 0.8) !important;
}

.toolbar button.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.toolbar .icon-button {
  width: 36px;
  height: 36px;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--icon-sm);
  position: relative;
  overflow: hidden;
  background-color: var(--neutral-50);
  border: 1px solid var(--neutral-200);
  color: var(--neutral-700);
  transition: all 0.2s ease;
  cursor: pointer;
}

.toolbar .icon-button:hover {
  background-color: var(--neutral-100);
  border-color: var(--neutral-300);
  color: var(--neutral-800);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.toolbar .icon-button.active {
  background-color: var(--primary-500);
  color: white;
  border-color: var(--primary-600);
  box-shadow: var(--shadow-md);
}

.toolbar .icon-button.active:hover {
  background-color: var(--primary-600);
  border-color: var(--primary-700);
}

.toolbar .icon-button:disabled {
  background-color: var(--neutral-100);
  color: var(--neutral-400);
  border-color: var(--neutral-200);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Zoom percentage styling */
.zoom-percentage {
  font-size: var(--space-3);
  font-weight: 600;
  color: var(--neutral-700);
  min-width: 48px;
  text-align: center;
}

/* Info button and instructions tooltip */
.info-button-container {
  position: relative;
  display: inline-block;
}

.instructions-tooltip {
  position: absolute;
  top: calc(100% + var(--space-2));
  right: 0;
  background: white;
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  padding: var(--space-4);
  min-width: 320px;
  max-width: 400px;
  z-index: 10000;
  animation: fadeInUp 0.2s ease-out;
}

.instructions-content h4 {
  margin: 0 0 var(--space-3) 0;
  color: var(--neutral-800);
  font-size: 1.1rem;
  font-weight: 600;
}

.instruction-section {
  margin-bottom: var(--space-3);
}

.instruction-section:last-child {
  margin-bottom: 0;
}

.instruction-section h5 {
  margin: 0 0 var(--space-2) 0;
  color: var(--neutral-700);
  font-size: 0.9rem;
  font-weight: 600;
}

.instruction-section ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.instruction-section li {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-1) 0;
  font-size: 0.85rem;
  color: var(--neutral-600);
}

.instruction-section li i {
  width: 16px;
  text-align: center;
  color: var(--primary-500);
}

.instruction-section kbd {
  background: var(--neutral-100);
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-sm);
  padding: 2px 6px;
  font-size: 0.75rem;
  font-family: monospace;
  color: var(--neutral-700);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: clamp(6px, 1vw, 12px);
  padding: 0 clamp(6px, 1vw, 12px);
}

.toolbar-section:not(:last-child) {
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.toolbar-section span {
  font-size: clamp(10px, 1vw, 13px);
  color: #666;
  white-space: nowrap;
  font-weight: 600;
  min-width: 45px;
  text-align: center;
}

.pdf-dropdown-container {
  position: relative;
  z-index: 1100; /* Ensure container has proper stacking context */
}

.pdf-dropdown {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border: 1px solid #ddd;
  border-radius: clamp(6px, 0.8vw, 12px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1200; /* Higher z-index to ensure it appears above toolbar */
  min-width: clamp(180px, 20vw, 250px);
  max-width: clamp(250px, 30vw, 400px);
  /* Remove max-height and overflow-y to allow natural overflow */
  margin-top: clamp(3px, 0.5vh, 8px);
  font-size: clamp(11px, 1vw, 13px);
}

.pdf-dropdown-item {
  padding: clamp(6px, 0.8vw, 10px) clamp(8px, 1.2vw, 15px);
  cursor: pointer;
  border-bottom: 1px solid #eee;
  font-size: clamp(11px, 1vw, 13px);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #333;
}

.pdf-dropdown-item:hover {
  background-color: #f5f5f5;
}

.pdf-dropdown-item.active {
  background-color: #e3f2fd;
  color: #1976d2;
  font-weight: bold;
}

.pdf-dropdown-item:last-child {
  border-bottom: none;
}

.main-content {
  display: flex;
  height: 100vh;
  padding-top: clamp(70px, 8vh, 120px); /* Space for floating toolbar */
  width: 100vw;
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
  transition: all 0.3s ease;
}

.main-content.panel-collapsed {
  /* When panel is collapsed, main content takes full width */
}

.canvas-container {
  flex: 1;
  overflow: hidden;
  padding: clamp(15px, 2vw, 30px);
  background-color: var(--neutral-100);
  background-image:
    linear-gradient(var(--neutral-200) 1px, transparent 1px),
    linear-gradient(90deg, var(--neutral-200) 1px, transparent 1px);
  background-size: 20px 20px;
  background-position: 0 0, 0 0;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - clamp(120px, 15vh, 200px));
  position: relative;
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
}



.annotations-list {
  width: clamp(280px, 25vw, 400px);
  min-width: 280px;
  padding: var(--space-4);
  background-color: white;
  border-left: 1px solid var(--neutral-200);
  overflow-y: auto;
  font-size: var(--text-sm);
  height: 100vh;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  position: relative;
  flex-shrink: 0;
}

.annotations-list.collapsed {
  width: 0;
  min-width: 0;
  padding: 0;
  border: none;
  overflow: hidden;
}

.annotations-panel-toggle {
  position: absolute;
  right: -20px;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 64px;
  background: white;
  border: 2px solid var(--neutral-300);
  border-radius: 0 var(--radius-md) var(--radius-md) 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--shadow-lg);
  transition: all 0.2s ease;
  z-index: 100;
}

.annotations-panel-toggle:hover {
  background: var(--neutral-50);
  border-color: var(--neutral-400);
  transform: translateY(-50%) translateX(3px);
  box-shadow: var(--shadow-xl);
}

.annotations-panel-toggle i {
  color: var(--neutral-700);
  font-size: 16px;
  font-weight: bold;
  transition: transform 0.2s ease;
}

.annotations-list.collapsed .annotations-panel-toggle {
  right: 0;
  border-radius: var(--radius-md) 0 0 var(--radius-md);
  position: fixed;
  right: 0;
  z-index: 200;
  width: 40px;
  border: 2px solid var(--neutral-300);
}

.annotations-list.collapsed .annotations-panel-toggle i {
  transform: rotate(180deg);
}

/* Overlap warning styling */
.overlap-warning-text {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  color: var(--error-600);
  font-size: 0.75rem;
  margin-top: var(--space-1);
  font-weight: 600;
}

.overlap-warning-text i {
  font-size: 0.7rem;
}

/* Overlap warning modal */
.overlap-warning-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
  animation: fadeIn 0.2s ease-out;
}

.overlap-warning-content {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  max-width: 500px;
  width: 90vw;
  padding: var(--space-6);
  animation: slideInUp 0.3s ease-out;
}

.overlap-warning-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.overlap-warning-header i {
  color: var(--warning-600);
  font-size: var(--text-xl);
}

.overlap-warning-header h3 {
  margin: 0;
  color: var(--neutral-800);
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
}

.overlap-warning-content p {
  margin: 0 0 var(--space-6) 0;
  color: var(--neutral-700);
  line-height: var(--leading-relaxed);
}

.overlap-warning-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
}

.overlap-warning-actions .cancel-button {
  padding: var(--space-2) var(--space-4);
  border: 1px solid var(--neutral-300);
  background: white;
  color: var(--neutral-700);
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  transition: all 0.2s ease;
}

.overlap-warning-actions .cancel-button:hover {
  background: var(--neutral-50);
  border-color: var(--neutral-400);
}

.overlap-warning-actions .confirm-button {
  padding: var(--space-2) var(--space-4);
  border: none;
  background: var(--warning-600);
  color: white;
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  transition: all 0.2s ease;
}

.overlap-warning-actions .confirm-button:hover {
  background: var(--warning-700);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* No annotations message */
.no-annotations {
  text-align: center;
  padding: var(--space-8);
  color: var(--neutral-500);
}

.no-annotations p {
  margin: 0;
  font-size: 0.875rem;
}

.annotations-list h3 {
  margin: 0 0 var(--space-4) 0;
  color: var(--neutral-800);
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-tight);
}

/* Progress Bar Styles */
.annotation-progress-section {
  margin-bottom: var(--space-4);
  padding: var(--space-3);
  background-color: var(--neutral-50);
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-md);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
  font-size: 0.875rem;
}

.progress-text {
  color: var(--neutral-700);
  font-weight: var(--font-weight-medium);
}

.progress-stats {
  color: var(--neutral-600);
  font-weight: var(--font-weight-semibold);
  font-size: 0.8rem;
}

.progress-bar-container {
  width: 100%;
  height: 8px;
  background-color: var(--neutral-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-500) 0%, var(--primary-600) 100%);
  border-radius: var(--radius-full);
  transition: width 0.3s ease;
  min-width: 2px; /* Ensure visibility even at 0% */
  position: relative;
}

/* Progress bar states */
.progress-bar-fill[data-percentage="100"] {
  background: linear-gradient(90deg, #10b981 0%, #059669 100%); /* Green for complete */
}

.progress-bar-fill[data-percentage="0"] {
  background: var(--neutral-300); /* Gray for no progress */
  min-width: 100%;
  opacity: 0.3;
}

/* Hover effect for progress bar */
.progress-bar-container:hover .progress-bar-fill {
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.4);
}

.annotation-items {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  flex: 1;
  overflow-y: auto;
  max-height: calc(100vh - 200px);
}

.annotation-item {
  padding: var(--space-3);
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-lg);
  background-color: var(--neutral-50);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.annotation-item:hover {
  background-color: white;
  border-color: var(--neutral-300);
  box-shadow: var(--shadow-sm);
  transform: translateY(-1px);
}

.annotation-label {
  font-weight: var(--font-weight-semibold);
  color: var(--neutral-800);
  margin-bottom: var(--space-2);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-sm);
  transition: background-color 0.2s ease;
  font-size: var(--text-sm);
  line-height: var(--leading-tight);
}

.annotation-label:hover {
  background-color: var(--neutral-100);
}

.annotation-label-input {
  width: 100%;
  padding: clamp(2px, 0.3vw, 4px);
  border: 1px solid #ccc;
  border-radius: clamp(2px, 0.3vw, 4px);
  font-size: inherit;
  font-weight: bold;
  background-color: white;
  color: #333 !important;
  outline: none;
}

.annotation-label-input:focus {
  border-color: #4caf50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.annotation-details {
  font-size: clamp(10px, 1vw, 12px);
  color: #666 !important;
}

.overlap-warning {
  background-color: #fff3cd !important;
  border-color: #ffc107 !important;
  color: #856404 !important;
}

.overlap-warning .annotation-label {
  color: #856404 !important;
}

.overlap-warning .annotation-details {
  color: #856404 !important;
}

.annotation-item.selected {
  background-color: var(--primary-50);
  border-color: var(--primary-500);
  box-shadow: 0 0 0 2px var(--primary-100);
}

.annotation-item span {
  display: block;
  margin-bottom: var(--space-2);
  font-size: var(--text-xs);
  color: var(--neutral-600);
  line-height: var(--leading-normal);
}

.annotation-controls {
  display: flex;
  gap: var(--space-2);
  margin-top: var(--space-2);
}

.annotation-controls button {
  padding: var(--space-1) var(--space-2);
  font-size: var(--text-xs);
  border: 1px solid var(--neutral-300);
  background-color: white;
  color: var(--neutral-700);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: var(--font-weight-medium);
}

.annotation-controls button:hover {
  background-color: var(--neutral-50);
  border-color: var(--neutral-400);
  color: var(--neutral-800);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.annotation-control-button {
  padding: clamp(3px, 0.5vw, 6px) clamp(6px, 0.8vw, 10px) !important;
  font-size: clamp(10px, 1vw, 12px) !important;
  border: 1px solid #ccc !important;
  background-color: white !important;
  color: #333 !important;
  border-radius: clamp(3px, 0.4vw, 6px) !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.annotation-control-button:hover {
  background-color: #f0f0f0 !important;
  border-color: #999 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  color: #222 !important;
}





.annotation-help {
  margin-top: 20px;
  padding: 15px;
  background-color: #f0f8ff;
  border: 1px solid #b3d9ff;
  border-radius: 4px;
  font-size: 12px;
}

.annotation-help p {
  margin: 0 0 10px 0;
  font-weight: bold;
  color: #1976d2;
}

.annotation-help ul {
  margin: 0;
  padding-left: 20px;
}

.annotation-help li {
  margin-bottom: 5px;
  color: #555;
}

.canvas-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  min-height: 400px;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  z-index: 1; /* Ensure canvas has a defined stacking context */
}

/* Ensure canvas and annotations remain visible during room assignment */
.canvas-wrapper canvas {
  z-index: 2; /* Canvas should be above the wrapper but below dropdowns */
  position: relative;
}

/* Ensure pending annotations remain visible during room assignment */
.canvas-wrapper.room-assignment-active {
  z-index: 1; /* Keep canvas below dropdown but ensure it's still visible */
}

.canvas-wrapper.room-assignment-active canvas {
  z-index: 2; /* Maintain canvas visibility */
  opacity: 1; /* Ensure full opacity */
}

.zoom-indicator {
  position: absolute;
  top: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: bold;
  z-index: 100;
  animation: zoomFade 1s ease-out;
  pointer-events: none;
}

.annotation-counter {
  position: absolute;
  top: 20px;
  left: 20px;
  background-color: rgba(255, 0, 0, 0.8);
  color: white;
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: bold;
  z-index: 100;
  pointer-events: none;
}

@keyframes zoomFade {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  20% {
    opacity: 1;
    transform: scale(1);
  }
  80% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
}

.pdf-canvas {
  border: 2px solid var(--neutral-300);
  background-color: white;
  cursor: crosshair;
  box-shadow: var(--shadow-xl);
  user-select: none;
  max-width: calc(100vw - clamp(40px, 8vw, 120px));
  max-height: calc(100vh - clamp(120px, 15vh, 200px));
  object-fit: contain;
  transition: transform 0.1s ease-out;
  display: block;
  margin: 0;
  border-radius: var(--radius-lg);
  position: relative;
  box-sizing: border-box;
  /* Touch and trackpad optimizations */
  touch-action: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.pdf-canvas.hand-cursor {
  cursor: grab;
}

.pdf-canvas.select-cursor {
  cursor: pointer;
}

/* Welcome Screen Styles */
.welcome-screen {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  width: 100%;
  height: 100%;
  min-height: calc(100vh - 120px);
  padding: 2rem;
  box-sizing: border-box;
  overflow-y: auto;
}

.welcome-content {
  max-width: 1000px;
  width: 100%;
  text-align: center;
  color: #333;
  margin: 0 auto;
}

.welcome-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.welcome-content h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  color: #2c3e50;
  font-weight: 600;
}

.welcome-subtitle {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 3rem;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.feature-item {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.feature-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.feature-item h3 {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  color: #2c3e50;
}

.feature-item p {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.4;
}

.getting-started {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  text-align: left;
}

.getting-started h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  text-align: center;
}

.getting-started ol {
  max-width: 500px;
  margin: 0 auto;
}

.getting-started li {
  margin-bottom: 0.5rem;
  color: #555;
}

.keyboard-shortcuts {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.keyboard-shortcuts h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.shortcuts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.5rem;
  font-size: 0.9rem;
}

.shortcuts-grid div {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #555;
}

kbd {
  background: #f1f3f4;
  border: 1px solid #dadce0;
  border-radius: 4px;
  padding: 2px 6px;
  font-family: monospace;
  font-size: 0.8rem;
  color: #333;
}

/* Mobile responsive design */
@media (max-width: 768px) {
  .welcome-screen {
    padding: 1rem;
    align-items: flex-start;
  }

  .welcome-content {
    padding: 0;
    max-width: 100%;
  }

  .welcome-content h1 {
    font-size: 2rem;
  }

  .feature-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .shortcuts-grid {
    grid-template-columns: 1fr 1fr;
  }

  .getting-started,
  .keyboard-shortcuts {
    padding: 1.5rem;
  }

  .canvas-container {
    padding: clamp(10px, 2vw, 20px);
  }

  .main-content {
    width: 100vw;
    overflow-x: hidden;
  }
}

@media (max-width: 480px) {
  .welcome-icon {
    font-size: 3rem;
  }

  .welcome-content h1 {
    font-size: 1.8rem;
  }

  .welcome-subtitle {
    font-size: 1rem;
  }

  .shortcuts-grid {
    grid-template-columns: 1fr;
  }
}

.pdf-canvas.hand-cursor:active {
  cursor: grabbing;
}

/* Responsive design for different screen sizes */

/* Large screens (1920px and above) */
@media (min-width: 1920px) {
  .toolbar {
    gap: 25px;
    padding: 18px 30px;
    border-radius: 35px;
  }

  .toolbar .icon-button {
    width: 40px;
    height: 40px;
    font-size: var(--icon-md);
  }

  .toolbar button {
    font-size: 16px;
    padding: 12px 20px;
  }

  .toolbar-section span {
    font-size: 15px;
  }

  .annotations-list {
    width: 450px;
    padding: 30px;
    font-size: 16px;
  }

  .annotations-list h3 {
    font-size: 20px;
  }
}

/* Medium-large screens (1200px to 1919px) */
@media (min-width: 1200px) and (max-width: 1919px) {
  .toolbar {
    gap: 20px;
    padding: 15px 25px;
  }

  .toolbar .icon-button {
    width: 38px;
    height: 38px;
    font-size: var(--icon-md);
  }

  .annotations-list {
    width: 350px;
    padding: 25px;
  }
}

/* Tablet screens (768px to 1199px) */
@media (min-width: 768px) and (max-width: 1199px) {
  .toolbar {
    gap: 15px;
    padding: 12px 20px;
  }

  .toolbar .icon-button {
    width: 36px;
    height: 36px;
    font-size: var(--icon-sm);
  }

  .annotations-list {
    width: 300px;
    padding: 20px;
  }
}

/* Mobile screens (up to 767px) */
@media (max-width: 767px) {
  .toolbar {
    top: 10px;
    left: 10px;
    right: 10px;
    transform: none;
    max-width: none;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 20px;
    flex-wrap: wrap;
  }

  .toolbar-section {
    gap: 4px;
    padding: 0 4px;
  }

  .toolbar-section span {
    font-size: 10px;
  }

  .toolbar .icon-button {
    width: 32px;
    height: 32px;
    font-size: var(--icon-sm);
  }

  .main-content {
    flex-direction: column;
    padding-top: 80px;
  }

  .canvas-container {
    padding: 10px;
    align-items: flex-start;
  }

  .canvas-wrapper {
    min-height: 300px;
  }

  .annotations-list {
    width: 100%;
    max-height: 200px;
    padding: 15px;
    font-size: 12px;
  }

  .annotations-list h3 {
    font-size: 14px;
  }

  /* Mobile progress bar adjustments */
  .annotation-progress-section {
    padding: var(--space-2);
    margin-bottom: var(--space-3);
  }

  .progress-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-1);
  }

  .progress-text {
    font-size: 0.75rem;
  }

  .progress-stats {
    font-size: 0.7rem;
  }

  .progress-bar-container {
    height: 6px;
  }



  .pdf-dropdown {
    min-width: 150px;
    max-width: 250px;
    font-size: 11px;
  }

  .pdf-dropdown-item {
    padding: 6px 8px;
    font-size: 11px;
  }
}

/* Ultra-wide screens (2560px and above) */
@media (min-width: 2560px) {
  .toolbar {
    gap: 30px;
    padding: 20px 35px;
    border-radius: 40px;
  }

  .toolbar .icon-button {
    width: 44px;
    height: 44px;
    font-size: var(--icon-lg);
  }

  .toolbar button {
    font-size: 18px;
    padding: 14px 24px;
  }

  .toolbar-section span {
    font-size: 16px;
  }

  .annotations-list {
    width: 500px;
    padding: 35px;
    font-size: 18px;
  }

  .annotations-list h3 {
    font-size: 22px;
  }

  .annotation-item {
    padding: 15px;
  }

  .annotation-item span {
    font-size: 16px;
  }
}

/* Room Name Dropdown Styles */
.room-name-dropdown {
  background: white;
  border: 2px solid #4caf50;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 300px;
  max-width: 400px;
  max-height: 400px;
  display: flex;
  flex-direction: column;
  z-index: 10000; /* Ensure dropdown appears above all other elements */
  position: fixed; /* Ensure proper positioning context */
}

.room-dropdown-header {
  padding: 12px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}

.room-dropdown-header h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 14px;
  font-weight: bold;
}

.room-search-input {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 12px;
  color: #333 !important;
  background-color: white;
  outline: none;
}

.room-search-input:focus {
  border-color: #4caf50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.room-dropdown-list {
  flex: 1;
  overflow-y: auto;
  max-height: 250px;
}

.room-dropdown-item {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 12px;
  color: #333 !important;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.room-dropdown-item:hover,
.room-dropdown-item.selected {
  background-color: #e8f5e8;
}

.room-dropdown-item.no-results {
  color: #666;
  font-style: italic;
  cursor: default;
}

.room-dropdown-item.no-results:hover {
  background-color: transparent;
}

.room-dropdown-footer {
  padding: 8px 12px;
  border-top: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.room-dropdown-footer button {
  padding: 4px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s ease;
}

.cancel-button:hover {
  background-color: #f5f5f5;
  border-color: #999;
}

.cancel-button {
  color: black !important;
}

.select-button {
  background-color: #4caf50 !important;
  color: white !important;
  border-color: #4caf50 !important;
}

.select-button:hover {
  background-color: #45a049 !important;
  border-color: #45a049 !important;
}

/* CSV Upload Button Styles */
.csv-upload-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.csv-status {
  font-size: var(--text-sm);
  color: var(--success-700) !important;
  font-weight: var(--font-weight-semibold) !important;
  max-width: 140px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 36px; /* Match toolbar button height */
  padding: 0 var(--space-3);
  background: var(--success-50) !important;
  border: 1px solid var(--success-200);
  border-radius: var(--radius-lg);
  line-height: var(--leading-tight);
  min-width: 80px;
  box-shadow: var(--shadow-sm);
}

.csv-status.loaded {
  color: var(--success-700) !important;
  font-weight: var(--font-weight-semibold) !important;
  background: var(--success-50) !important;
}

/* Hierarchical Room Filter Styles */
.hierarchical-filter-overlay {
  background: white;
  border: 2px solid #4caf50;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 400px;
  max-width: 600px;
  max-height: 500px;
  display: flex;
  flex-direction: column;
}

.hierarchical-room-filter {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;
}

.filter-header h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
  font-weight: bold;
}

.clear-filters-btn {
  padding: 4px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 11px;
  color: #666;
  transition: all 0.2s ease;
}

.clear-filters-btn:hover {
  background-color: #f0f0f0;
  border-color: #999;
}

.filter-levels {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.filter-level {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.filter-level-label {
  font-size: 12px;
  font-weight: bold;
  color: #555;
  margin-bottom: 4px;
}

.filter-level-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  background: white;
  color: #333;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.filter-level-select:focus {
  outline: none;
  border-color: #4caf50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.selected-path {
  padding: 8px 12px;
  background-color: #e8f5e8;
  border-radius: 4px;
  font-size: 12px;
  color: #2e7d32;
  border: 1px solid #c8e6c9;
}

.filtered-rooms {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filtered-rooms h5 {
  margin: 0;
  font-size: 13px;
  color: #333;
  font-weight: bold;
}

.rooms-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px;
}

.room-item {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background: white;
  color: #333;
  cursor: pointer;
  font-size: 12px;
  text-align: left;
  transition: all 0.2s ease;
}

.room-item:hover {
  background-color: #e8f5e8;
  border-color: #4caf50;
}

.no-rooms-message {
  padding: 12px;
  text-align: center;
  color: #666;
  font-style: italic;
  font-size: 12px;
}

.filter-message {
  padding: 16px;
  text-align: center;
  color: #666;
  font-size: 12px;
}

.filter-actions {
  padding: 12px 16px;
  border-top: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  display: flex;
  justify-content: flex-end;
}

.filter-actions .cancel-button {
  padding: 6px 16px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.filter-actions .cancel-button:hover {
  background-color: #f0f0f0;
}

.filter-actions .clear-button {
  padding: 6px 12px;
  border: 1px solid #ff9800;
  background: white;
  color: #ff9800;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.filter-actions .clear-button:hover {
  background-color: #fff3e0;
}

/* Responsive styles for hierarchical filter */
@media (max-width: 768px) {
  .hierarchical-filter-overlay {
    min-width: 300px;
    max-width: 90vw;
    max-height: 80vh;
  }

  .hierarchical-room-filter {
    padding: 12px;
    gap: 12px;
  }

  .filter-header h4 {
    font-size: 14px;
  }

  .filter-level-select {
    padding: 6px 8px;
    font-size: 11px;
  }

  .room-item {
    padding: 6px 8px;
    font-size: 11px;
  }

  .rooms-list {
    max-height: 150px;
  }
}

@media (max-width: 480px) {
  .hierarchical-filter-overlay {
    min-width: 280px;
    max-width: 95vw;
  }

  .filter-levels {
    gap: 8px;
  }

  .filter-level-label {
    font-size: 11px;
  }

  .selected-path {
    font-size: 11px;
    padding: 6px 8px;
  }
}

/* Pre-filter modal styles */
.pre-filter-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.pre-filter-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 90vw;
  max-height: 80vh;
  overflow-y: auto;
}

.pre-filter-header {
  padding: 20px 20px 10px 20px;
  border-bottom: 1px solid #e0e0e0;
  text-align: center;
}

.pre-filter-header h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 18px;
}

.pre-filter-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.pre-filter .hierarchical-room-filter {
  padding: 20px;
  border: none;
  box-shadow: none;
}

.pre-filter-actions {
  padding: 15px 20px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.pre-filter-actions .skip-button {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  color: #666;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.pre-filter-actions .skip-button:hover {
  background-color: #f5f5f5;
}

.pre-filter-actions .apply-button {
  padding: 8px 16px;
  border: none;
  background: #4caf50;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.pre-filter-actions .apply-button:hover {
  background-color: #45a049;
}

/* Toolbar filter control styles */
.filter-control-section {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 10px;
  padding-left: 10px;
  border-left: 1px solid #ddd;
}

.filter-status {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  background-color: var(--primary-50);
  border: 1px solid var(--primary-200);
  border-radius: var(--radius-xl);
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
  height: 28px;
  transition: all 0.2s ease;
  max-width: 200px;
}

.filter-status:hover {
  background-color: var(--primary-100);
  border-color: var(--primary-300);
}

.filter-path {
  color: var(--primary-700);
  font-weight: var(--font-weight-medium);
  max-width: 140px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: var(--leading-tight);
}

.clear-filter-btn {
  background: none;
  border: none;
  color: var(--primary-600);
  cursor: pointer;
  padding: var(--space-1);
  width: 18px;
  height: 18px;
  font-size: var(--text-xs);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
}

.clear-filter-btn:hover {
  background-color: var(--primary-200);
  color: var(--primary-800);
}

/* Active filter button styling */
.icon-button.active {
  background-color: #4caf50;
  color: white;
}

.icon-button.active:hover {
  background-color: #45a049;
}

/* Enhanced hierarchical filter styles */
.filter-summary {
  margin: 15px 0;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #4caf50;
}

.selected-path {
  color: #2e7d32;
  font-weight: 500;
  margin-bottom: 8px;
}

.filter-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.rooms-count {
  font-weight: 500;
  color: #4caf50;
}

.continue-hint {
  font-style: italic;
  color: #888;
}

.filter-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 15px 0;
  padding: 12px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 6px;
}

.apply-filter-btn {
  padding: 10px 16px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 13px;
  transition: background-color 0.2s ease;
}

.apply-filter-btn:hover {
  background-color: #45a049;
}

.continue-text {
  font-size: 12px;
  color: #666;
  font-style: italic;
}

.filtered-rooms h5 {
  margin: 15px 0 8px 0;
  color: #333;
  font-size: 13px;
}

.rooms-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  max-height: 120px;
  overflow-y: auto;
}

.room-item {
  padding: 6px 10px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 3px;
  cursor: pointer;
  font-size: 11px;
  color: #333;
  transition: all 0.2s ease;
}

.room-item:hover {
  background-color: #e8f5e8;
  border-color: #4caf50;
  color: #2e7d32;
}

.more-rooms-hint {
  padding: 6px 10px;
  color: #666;
  font-size: 11px;
  font-style: italic;
  background-color: #f9f9f9;
  border: 1px dashed #ccc;
  border-radius: 3px;
}

/* Custom room input styles */
.input-mode-toggle {
  display: flex;
  gap: 4px;
  margin-bottom: 8px;
}

.mode-toggle-btn {
  padding: 4px 8px;
  border: 1px solid #ddd;
  background: white;
  color: #666;
  border-radius: 3px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s ease;
}

.mode-toggle-btn.active {
  background-color: #4caf50;
  color: white;
  border-color: #4caf50;
}

.mode-toggle-btn:hover:not(.active) {
  background-color: #f5f5f5;
  border-color: #999;
}

.room-custom-input {
  width: 100%;
  padding: 8px;
  border: 2px solid #4caf50;
  border-radius: 4px;
  font-size: 12px;
  background-color: #f8fff8;
}

.room-custom-input:focus {
  outline: none;
  border-color: #45a049;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.custom-room-preview {
  padding: 15px;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8fff8;
  border: 1px solid #e8f5e8;
  border-radius: 4px;
}

.custom-room-display {
  text-align: center;
}

.custom-room-label {
  display: block;
  font-size: 11px;
  color: #666;
  margin-bottom: 4px;
}

.custom-room-name {
  font-size: 14px;
  font-weight: 500;
  color: #2e7d32;
  background-color: white;
  padding: 4px 8px;
  border-radius: 3px;
  border: 1px solid #4caf50;
}

.custom-room-hint {
  font-size: 12px;
  color: #888;
  text-align: center;
  font-style: italic;
}

.switch-to-custom-btn {
  display: block;
  margin: 8px auto 0;
  padding: 4px 8px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 10px;
}

.switch-to-custom-btn:hover {
  background-color: #45a049;
}

.add-custom-button {
  padding: 6px 12px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
}

.add-custom-button:hover:not(:disabled) {
  background-color: #45a049;
}

/* Distance-Sorted Room Dropdown Styles */
.room-name-dropdown.distance-sorted {
  min-width: 350px;
  max-width: 450px;
}

.close-rooms-indicator {
  font-size: 11px;
  color: #4caf50;
  font-weight: normal;
  margin-left: 8px;
}

.header-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.distance-toggle-btn {
  padding: 4px 8px;
  border: 1px solid #ddd;
  background: white;
  color: #666;
  border-radius: 3px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s ease;
}

.distance-toggle-btn:hover {
  background-color: #f5f5f5;
  border-color: #999;
}

.close-rooms-header,
.other-rooms-header {
  padding: 6px 12px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  font-size: 11px;
  font-weight: 500;
  color: #666;
  position: sticky;
  top: 0;
  z-index: 1;
}

.section-label {
  display: flex;
  align-items: center;
  gap: 4px;
}

.room-item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.room-name-section {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.distance-indicator {
  font-size: 14px;
  flex-shrink: 0;
}

.room-name {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.distance-info {
  flex-shrink: 0;
  margin-left: 8px;
}

.distance-text {
  font-size: 10px;
  color: #888;
  background-color: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
}

.room-dropdown-item.close-room {
  background-color: #f8fff8;
  border-left: 3px solid #4caf50;
}

.room-dropdown-item.very-close-room {
  background-color: #e8f5e8;
  border-left: 3px solid #2e7d32;
  font-weight: 500;
}

.room-dropdown-item.close-room:hover,
.room-dropdown-item.close-room.selected {
  background-color: #e8f5e8;
}

.room-dropdown-item.very-close-room:hover,
.room-dropdown-item.very-close-room.selected {
  background-color: #c8e6c8;
}

.room-dropdown-item.close-room .distance-text {
  background-color: #4caf50;
  color: white;
}

.room-dropdown-item.very-close-room .distance-text {
  background-color: #2e7d32;
  color: white;
  font-weight: bold;
}

.add-custom-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.custom-mode-button {
  padding: 6px 10px;
  background-color: #2196f3;
  color: black;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  margin-left: 8px;
}

.custom-mode-button:hover {
  background-color: #1976d2;
}

/* Interactive Hierarchical Filter Styles */
.interactive-hierarchical-filter {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 500px;
  max-height: 600px;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  background-color: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.filter-header h4 {
  margin: 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  color: #666;
  padding: 4px;
  border-radius: 3px;
}

.close-button:hover {
  background-color: #f0f0f0;
  color: #333;
}

.filter-instructions {
  padding: 8px 16px;
  font-size: 11px;
  color: #666;
  background-color: #f9f9f9;
  border-bottom: 1px solid #eee;
  font-style: italic;
}

.current-selection {
  padding: 12px 16px;
  background-color: #f0f8ff;
  border-bottom: 1px solid #e0e8f0;
}

.selected-path {
  color: #2e7d32;
  font-weight: 500;
  margin-bottom: 6px;
  font-size: 12px;
}

.selection-stats {
  margin-bottom: 8px;
}

.rooms-count {
  font-size: 11px;
  color: #4caf50;
  font-weight: 500;
}

.apply-filter-btn {
  padding: 6px 12px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  font-weight: 500;
}

.apply-filter-btn:hover {
  background-color: #45a049;
}

.tree-container {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
  max-height: 300px;
}

.tree-node {
  user-select: none;
}

.tree-item {
  margin: 1px 0;
}

.tree-item.selected .folder-button {
  background-color: #e3f2fd;
  border-left: 3px solid #2196f3;
}

.tree-item-content {
  display: flex;
  align-items: center;
  padding: 2px 8px;
}

.expand-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px 4px;
  margin-right: 4px;
  color: #666;
  font-size: 10px;
  width: 16px;
  text-align: center;
}

.expand-button:hover {
  background-color: #f0f0f0;
  border-radius: 2px;
}

.folder-button {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 6px;
  border-radius: 4px;
  flex: 1;
  text-align: left;
  font-size: 12px;
}

.folder-button:hover {
  background-color: #f5f5f5;
}

.folder-icon {
  font-size: 14px;
}

.folder-name {
  color: #333;
  font-weight: 500;
}

.room-count {
  color: #666;
  font-size: 10px;
  margin-left: auto;
}

.tree-children {
  margin-left: 0;
}

.room-selection-section {
  padding: 12px 16px;
  border-top: 1px solid #eee;
  background-color: #fafafa;
}

.room-selection-section h5 {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #333;
}

.rooms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 4px;
  max-height: 120px;
  overflow-y: auto;
}

.room-button {
  padding: 4px 6px;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 3px;
  cursor: pointer;
  font-size: 10px;
  color: #333;
  text-align: center;
}

.room-button:hover {
  background-color: #e8f5e8;
  border-color: #4caf50;
  color: #2e7d32;
}

.more-rooms-indicator {
  padding: 4px 6px;
  color: #666;
  font-size: 10px;
  font-style: italic;
  background-color: #f9f9f9;
  border: 1px dashed #ccc;
  border-radius: 3px;
  text-align: center;
}

.no-data-message {
  padding: 20px;
  text-align: center;
  color: #666;
  font-style: italic;
}

/* Landing Page Styles */
.landing-page {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
  padding: var(--space-4);
  overflow: hidden;
  font-family: var(--font-family-primary);
}

.landing-content {
  max-width: 1100px;
  width: 100%;
  height: calc(100vh - 30px);
  background: white;
  border-radius: 15px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  padding: 20px;
  text-align: center;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.landing-main {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 25px;
  flex: 1;
  text-align: left;
  align-items: start;
  overflow: hidden;
}

.landing-header h1 {
  font-size: var(--text-3xl);
  color: var(--neutral-800);
  margin-bottom: var(--space-2);
  font-weight: var(--font-weight-bold);
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-tight);
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.landing-header h1 i {
  color: var(--primary-600);
}

.landing-subtitle {
  font-size: var(--text-lg);
  color: var(--neutral-600);
  margin-bottom: var(--space-5);
  line-height: var(--leading-relaxed);
  font-weight: var(--font-weight-normal);
}

.upload-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
  height: 100%;
  overflow-y: auto;
}

.instructions-sidebar {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
  overflow-y: auto;
}

.upload-card {
  background: var(--neutral-50);
  border: 2px dashed var(--neutral-300);
  border-radius: var(--radius-xl);
  padding: var(--space-5);
  transition: all 0.3s ease;
  text-align: center;
}

.upload-card:hover {
  border-color: var(--primary-400);
  background: var(--primary-50);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.upload-card.secondary {
  background: var(--success-50);
  border-color: var(--success-200);
}

.upload-card.secondary:hover {
  border-color: var(--success-400);
  background: var(--success-100);
}

.upload-icon {
  font-size: 3rem;
  margin-bottom: var(--space-3);
  color: var(--primary-600);
}

.upload-card.secondary .upload-icon {
  color: var(--success-600);
}

.upload-card h2 {
  font-size: var(--text-xl);
  color: var(--neutral-800);
  margin-bottom: var(--space-2);
  font-weight: var(--font-weight-semibold);
  line-height: var(--leading-tight);
}

.upload-card h3 {
  font-size: var(--text-lg);
  color: var(--neutral-800);
  margin-bottom: var(--space-2);
  font-weight: var(--font-weight-semibold);
  line-height: var(--leading-tight);
}

.upload-card p {
  color: var(--neutral-600);
  margin-bottom: var(--space-4);
  line-height: var(--leading-relaxed);
  font-size: var(--text-base);
}

.upload-button {
  background: var(--primary-600);
  color: white;
  border: none;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: var(--space-2);
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  font-family: var(--font-family-primary);
}

.upload-button:hover {
  background: var(--primary-700);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.upload-button.secondary {
  background: var(--success-600);
}

.upload-button.secondary:hover {
  background: var(--success-700);
  box-shadow: 0 5px 15px rgba(34, 197, 94, 0.3);
}

.upload-hint {
  font-size: 0.9rem;
  color: #999;
  margin: 0;
}

.divider {
  display: flex;
  align-items: center;
  margin: 20px 0;
}

.divider::before,
.divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background: #dee2e6;
}

.divider span {
  padding: 0 20px;
  color: #999;
  font-size: 0.9rem;
  background: white;
}



.features-section {
  margin-top: 30px;
}

.features-section h3 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 25px;
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
  text-align: left;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.feature-icon {
  font-size: 1.5rem;
  margin-top: 5px;
}

.feature-item h4 {
  font-size: 1.1rem;
  color: #333;
  margin-bottom: 5px;
}

.feature-item p {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.4;
  margin: 0;
}

.instructions-section {
  text-align: left;
}

.instructions-section h3 {
  font-size: 1.3rem;
  color: #333;
  margin-bottom: 15px;
  text-align: center;
}

.instructions-list {
  list-style: none;
  counter-reset: step-counter;
  padding: 0;
}

.instructions-list li {
  counter-increment: step-counter;
  margin-bottom: 12px;
  padding-left: 35px;
  position: relative;
  line-height: 1.4;
  color: #555;
  font-size: 0.95rem;
}

.instructions-list li::before {
  content: counter(step-counter);
  position: absolute;
  left: 0;
  top: 0;
  background: #667eea;
  color: white;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.8rem;
}

/* Navbar Instructions Styles */
.navbar-instructions {
  text-align: left;
  background: #f8f9fa;
  padding: 18px;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.navbar-instructions h3 {
  font-size: 1.2rem;
  color: #333;
  margin-bottom: 12px;
  text-align: center;
}

.navbar-intro {
  color: #666;
  margin-bottom: 18px;
  text-align: center;
  font-size: 0.95rem;
  line-height: 1.4;
}

.navbar-tools {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
  margin-bottom: 15px;
}

.tool-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 10px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
  font-size: 0.85rem;
}

.tool-item:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.tool-icon {
  font-size: 1.1rem;
  margin-top: 1px;
  flex-shrink: 0;
}

.tool-item strong {
  color: #333;
  font-weight: 600;
}

.tool-item div {
  color: #555;
  font-size: 0.95rem;
  line-height: 1.4;
}

.navbar-tip {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 20px;
  border-radius: 10px;
  text-align: center;
  font-size: 1rem;
  line-height: 1.5;
}

/* Toast Styles */
.toast {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2000;
  max-width: 400px;
  min-width: 300px;
  border-radius: 10px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  transform: translateX(100%);
  opacity: 0;
}

.toast-show {
  transform: translateX(0);
  opacity: 1;
}

.toast-hide {
  transform: translateX(100%);
  opacity: 0;
}

.toast-loading {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.toast-success {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.toast-error {
  background: linear-gradient(135deg, #dc3545, #fd7e14);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.toast-warning {
  background: linear-gradient(135deg, #ffc107, #fd7e14);
  color: #333;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.toast-info {
  background: linear-gradient(135deg, #17a2b8, #6f42c1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
}

.toast-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.toast-message {
  flex: 1;
  font-weight: 500;
  line-height: 1.4;
}

.toast-spinner {
  flex-shrink: 0;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.toast-close {
  background: none;
  border: none;
  color: inherit;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
  flex-shrink: 0;
}

.toast-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .landing-page {
    padding: 10px;
  }

  .landing-content {
    height: calc(100vh - 20px);
    padding: 15px;
  }

  .landing-header h1 {
    font-size: 1.8rem;
    margin-bottom: 5px;
  }

  .landing-subtitle {
    font-size: 1rem;
    margin-bottom: 15px;
  }

  .landing-main {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .upload-card {
    padding: 15px;
  }

  .navbar-instructions {
    padding: 15px;
  }
}

  .landing-header h1 {
    font-size: 2rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .upload-card {
    padding: 20px;
  }

  .upload-button {
    padding: 12px 25px;
    font-size: 1rem;
  }

  .toast {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
    min-width: auto;
  }

  .navbar-tools {
    grid-template-columns: 1fr;
  }

  .tool-item {
    padding: 12px;
  }
